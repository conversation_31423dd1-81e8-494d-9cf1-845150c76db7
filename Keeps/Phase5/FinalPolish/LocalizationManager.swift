//
//  LocalizationManager.swift
//  Keeps
//
//  Created by SwiftMaster on Phase 5 Implementation
//

import Foundation
import SwiftUI
import Combine
import os.log

/// Comprehensive localization management system for multi-language support
class LocalizationManager: ObservableObject {
    
    // MARK: - Singleton
    
    static let shared = LocalizationManager()
    
    // MARK: - Published Properties
    
    @Published var currentLanguage: SupportedLanguage = .english
    @Published var availableLanguages: [SupportedLanguage] = SupportedLanguage.allCases
    @Published var isLoading = false
    @Published var localizationProgress: Double = 0.0
    @Published var localizationStatus: LocalizationStatus = .ready
    @Published var missingTranslations: [MissingTranslation] = []
    @Published var localizationReport: LocalizationReport?
    
    // MARK: - Private Properties
    
    private let logger = Logger(subsystem: "com.keeps.localization", category: "manager")
    private var localizationCancellables = Set<AnyCancellable>()
    private var translations: [String: [String: String]] = [:]
    private var pluralRules: [String: PluralRule] = [:]
    
    // MARK: - Initialization
    
    private init() {
        setupLocalization()
        loadTranslations()
        detectSystemLanguage()
    }
    
    // MARK: - Public Methods
    
    /// Get localized string
    func localizedString(for key: String, arguments: [String] = []) -> String {
        let languageCode = currentLanguage.code
        
        guard let languageTranslations = translations[languageCode],
              let translation = languageTranslations[key] else {
            // Fallback to English if translation not found
            if let englishTranslations = translations[SupportedLanguage.english.code],
               let englishTranslation = englishTranslations[key] {
                recordMissingTranslation(key: key, language: currentLanguage)
                return formatString(englishTranslation, arguments: arguments)
            }
            
            // Return key if no translation found
            recordMissingTranslation(key: key, language: currentLanguage)
            return key
        }
        
        return formatString(translation, arguments: arguments)
    }
    
    /// Get localized plural string
    func localizedPluralString(for key: String, count: Int, arguments: [String] = []) -> String {
        let languageCode = currentLanguage.code
        let pluralForm = getPluralForm(for: count, language: currentLanguage)
        let pluralKey = "\(key).\(pluralForm)"
        
        return localizedString(for: pluralKey, arguments: arguments)
    }
    
    /// Change current language
    func changeLanguage(to language: SupportedLanguage) async {
        await MainActor.run {
            isLoading = true
            localizationProgress = 0.0
        }
        
        logger.info("Changing language to: \(language.rawValue)")
        
        // Load language-specific resources
        await loadLanguageResources(for: language)
        
        await MainActor.run {
            currentLanguage = language
            isLoading = false
            localizationProgress = 1.0
        }
        
        // Save language preference
        saveLanguagePreference(language)
        
        // Notify observers
        NotificationCenter.default.post(name: .languageDidChange, object: language)
        
        logger.info("Language changed successfully")
    }
    
    /// Validate localization completeness
    func validateLocalization() async {
        await MainActor.run {
            isLoading = true
            localizationProgress = 0.0
            localizationStatus = .validating
            missingTranslations.removeAll()
        }
        
        logger.info("Starting localization validation")
        
        var allMissingTranslations: [MissingTranslation] = []
        
        // Check each supported language
        for (index, language) in availableLanguages.enumerated() {
            await updateProgress(Double(index) / Double(availableLanguages.count))
            
            let missing = await validateLanguage(language)
            allMissingTranslations.append(contentsOf: missing)
        }
        
        // Generate localization report
        let report = LocalizationReport(
            totalKeys: getTotalTranslationKeys(),
            completedLanguages: getCompletedLanguages(),
            missingTranslations: allMissingTranslations,
            completionPercentage: calculateCompletionPercentage(),
            generatedAt: Date()
        )
        
        await MainActor.run {
            missingTranslations = allMissingTranslations
            localizationReport = report
            localizationStatus = allMissingTranslations.isEmpty ? .complete : .incomplete
            isLoading = false
            localizationProgress = 1.0
        }
        
        logger.info("Localization validation completed")
    }
    
    /// Export translations for external translation services
    func exportTranslations(format: ExportFormat) async -> Data? {
        logger.info("Exporting translations in \(format.rawValue) format")
        
        switch format {
        case .json:
            return exportAsJSON()
        case .csv:
            return exportAsCSV()
        case .xliff:
            return exportAsXLIFF()
        case .strings:
            return exportAsStrings()
        }
    }
    
    /// Import translations from external sources
    func importTranslations(data: Data, format: ExportFormat, language: SupportedLanguage) async -> Bool {
        logger.info("Importing translations for \(language.rawValue) in \(format.rawValue) format")
        
        do {
            switch format {
            case .json:
                try await importFromJSON(data: data, language: language)
            case .csv:
                try await importFromCSV(data: data, language: language)
            case .xliff:
                try await importFromXLIFF(data: data, language: language)
            case .strings:
                try await importFromStrings(data: data, language: language)
            }
            
            // Reload translations
            await loadTranslations()
            
            logger.info("Translations imported successfully")
            return true
        } catch {
            logger.error("Failed to import translations: \(error.localizedDescription)")
            return false
        }
    }
    
    /// Get localization statistics
    func getLocalizationStatistics() -> LocalizationStatistics {
        let totalKeys = getTotalTranslationKeys()
        var languageStats: [LanguageStatistics] = []
        
        for language in availableLanguages {
            let translatedKeys = getTranslatedKeysCount(for: language)
            let completionPercentage = totalKeys > 0 ? Double(translatedKeys) / Double(totalKeys) : 0.0
            
            languageStats.append(LanguageStatistics(
                language: language,
                translatedKeys: translatedKeys,
                totalKeys: totalKeys,
                completionPercentage: completionPercentage
            ))
        }
        
        return LocalizationStatistics(
            totalKeys: totalKeys,
            supportedLanguages: availableLanguages.count,
            averageCompletion: languageStats.map { $0.completionPercentage }.reduce(0, +) / Double(languageStats.count),
            languageStatistics: languageStats
        )
    }
    
    // MARK: - Private Methods
    
    private func setupLocalization() {
        // Setup localization monitoring
        logger.info("Localization system initialized")
    }
    
    private func loadTranslations() {
        // Load all translation files
        for language in availableLanguages {
            loadTranslationsForLanguage(language)
        }
        
        logger.info("Translations loaded for \(self.availableLanguages.count) languages")
    }
    
    private func loadTranslationsForLanguage(_ language: SupportedLanguage) {
        // Load translations from bundle or remote source
        let languageCode = language.code
        
        // Simulate loading translations
        translations[languageCode] = getDefaultTranslations(for: language)
        
        logger.debug("Loaded translations for \(language.rawValue)")
    }
    
    private func detectSystemLanguage() {
        let systemLanguageCode = Locale.current.language.languageCode?.identifier ?? "en"
        
        if let detectedLanguage = SupportedLanguage.allCases.first(where: { $0.code == systemLanguageCode }) {
            currentLanguage = detectedLanguage
            logger.info("Detected system language: \(detectedLanguage.rawValue)")
        } else {
            currentLanguage = .english
            logger.info("Using default language: English")
        }
    }
    
    private func loadLanguageResources(for language: SupportedLanguage) async {
        // Load language-specific resources (fonts, images, etc.)
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        await updateProgress(0.5)
        
        // Load additional resources
        try? await Task.sleep(nanoseconds: 300_000_000) // 0.3 seconds
        
        await updateProgress(1.0)
    }
    
    private func updateProgress(_ progress: Double) async {
        await MainActor.run {
            localizationProgress = progress
        }
    }
    
    private func saveLanguagePreference(_ language: SupportedLanguage) {
        UserDefaults.standard.set(language.code, forKey: "selectedLanguage")
        logger.debug("Language preference saved: \(language.rawValue)")
    }
    
    private func formatString(_ string: String, arguments: [String]) -> String {
        var formattedString = string
        
        for (index, argument) in arguments.enumerated() {
            formattedString = formattedString.replacingOccurrences(of: "%\(index + 1)", with: argument)
        }
        
        return formattedString
    }
    
    private func getPluralForm(for count: Int, language: SupportedLanguage) -> String {
        let rule = pluralRules[language.code] ?? .english
        
        switch rule {
        case .english:
            return count == 1 ? "one" : "other"
        case .slavic:
            if count % 10 == 1 && count % 100 != 11 {
                return "one"
            } else if count % 10 >= 2 && count % 10 <= 4 && (count % 100 < 10 || count % 100 >= 20) {
                return "few"
            } else {
                return "many"
            }
        case .arabic:
            switch count {
            case 0: return "zero"
            case 1: return "one"
            case 2: return "two"
            case 3...10: return "few"
            case 11...99: return "many"
            default: return "other"
            }
        }
    }
    
    private func recordMissingTranslation(key: String, language: SupportedLanguage) {
        let missingTranslation = MissingTranslation(
            key: key,
            language: language,
            context: "General",
            priority: .medium
        )
        
        DispatchQueue.main.async { [weak self] in
            if let self = self, !self.missingTranslations.contains(where: { $0.key == key && $0.language == language }) {
                self.missingTranslations.append(missingTranslation)
            }
        }
    }
    
    private func validateLanguage(_ language: SupportedLanguage) async -> [MissingTranslation] {
        var missing: [MissingTranslation] = []
        
        let baseKeys = Set(translations[SupportedLanguage.english.code]?.keys ?? Dictionary<String, String>().keys)
        let languageKeys = Set(translations[language.code]?.keys ?? Dictionary<String, String>().keys)
        
        let missingKeys = baseKeys.subtracting(languageKeys)
        
        for key in missingKeys {
            missing.append(MissingTranslation(
                key: key,
                language: language,
                context: "Validation",
                priority: .high
            ))
        }
        
        return missing
    }
    
    private func getTotalTranslationKeys() -> Int {
        return translations[SupportedLanguage.english.code]?.count ?? 0
    }
    
    private func getCompletedLanguages() -> [SupportedLanguage] {
        let totalKeys = getTotalTranslationKeys()
        
        return availableLanguages.filter { language in
            let translatedKeys = getTranslatedKeysCount(for: language)
            return translatedKeys == totalKeys
        }
    }
    
    private func getTranslatedKeysCount(for language: SupportedLanguage) -> Int {
        return translations[language.code]?.count ?? 0
    }
    
    private func calculateCompletionPercentage() -> Double {
        let totalKeys = getTotalTranslationKeys()
        guard totalKeys > 0 else { return 0.0 }
        
        let totalTranslated = availableLanguages.reduce(0) { sum, language in
            sum + getTranslatedKeysCount(for: language)
        }
        
        let totalPossible = totalKeys * availableLanguages.count
        return Double(totalTranslated) / Double(totalPossible)
    }
    
    // MARK: - Export/Import Methods
    
    private func exportAsJSON() -> Data? {
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: translations, options: .prettyPrinted)
            return jsonData
        } catch {
            logger.error("Failed to export as JSON: \(error.localizedDescription)")
            return nil
        }
    }
    
    private func exportAsCSV() -> Data? {
        var csvContent = "Key,English"
        
        // Add language headers
        for language in availableLanguages where language != .english {
            csvContent += ",\(language.rawValue)"
        }
        csvContent += "\n"
        
        // Add translation rows
        let englishTranslations = translations[SupportedLanguage.english.code] ?? [:]
        
        for (key, englishValue) in englishTranslations {
            var row = "\(key),\(englishValue)"
            
            for language in availableLanguages where language != .english {
                let translation = translations[language.code]?[key] ?? ""
                row += ",\(translation)"
            }
            row += "\n"
            csvContent += row
        }
        
        return csvContent.data(using: .utf8)
    }
    
    private func exportAsXLIFF() -> Data? {
        // Simplified XLIFF export
        var xliffContent = """
        <?xml version="1.0" encoding="UTF-8"?>
        <xliff version="1.2">
        <file source-language="en" target-language="\(currentLanguage.code)">
        <body>
        """
        
        let englishTranslations = translations[SupportedLanguage.english.code] ?? [:]
        let targetTranslations = translations[currentLanguage.code] ?? [:]
        
        for (key, englishValue) in englishTranslations {
            let targetValue = targetTranslations[key] ?? ""
            xliffContent += """
            <trans-unit id="\(key)">
            <source>\(englishValue)</source>
            <target>\(targetValue)</target>
            </trans-unit>
            """
        }
        
        xliffContent += """
        </body>
        </file>
        </xliff>
        """
        
        return xliffContent.data(using: .utf8)
    }
    
    private func exportAsStrings() -> Data? {
        let languageTranslations = translations[currentLanguage.code] ?? [:]
        var stringsContent = ""
        
        for (key, value) in languageTranslations.sorted(by: { $0.key < $1.key }) {
            stringsContent += "\"\(key)\" = \"\(value)\";\n"
        }
        
        return stringsContent.data(using: .utf8)
    }
    
    private func importFromJSON(data: Data, language: SupportedLanguage) async throws {
        let jsonObject = try JSONSerialization.jsonObject(with: data, options: [])
        
        if let translationsDict = jsonObject as? [String: [String: String]] {
            translations = translationsDict
        } else if let languageDict = jsonObject as? [String: String] {
            translations[language.code] = languageDict
        } else {
            throw LocalizationError.invalidFormat
        }
    }
    
    private func importFromCSV(data: Data, language: SupportedLanguage) async throws {
        guard let csvString = String(data: data, encoding: .utf8) else {
            throw LocalizationError.invalidEncoding
        }
        
        let lines = csvString.components(separatedBy: .newlines)
        guard lines.count > 1 else {
            throw LocalizationError.invalidFormat
        }
        
        let headers = lines[0].components(separatedBy: ",")
        guard let languageIndex = headers.firstIndex(of: language.rawValue) else {
            throw LocalizationError.languageNotFound
        }
        
        var languageTranslations: [String: String] = [:]
        
        for line in lines.dropFirst() {
            let components = line.components(separatedBy: ",")
            guard components.count > languageIndex else { continue }
            
            let key = components[0]
            let translation = components[languageIndex]
            languageTranslations[key] = translation
        }
        
        translations[language.code] = languageTranslations
    }
    
    private func importFromXLIFF(data: Data, language: SupportedLanguage) async throws {
        // Simplified XLIFF import - would need proper XML parsing in production
        throw LocalizationError.notImplemented
    }
    
    private func importFromStrings(data: Data, language: SupportedLanguage) async throws {
        guard let stringsContent = String(data: data, encoding: .utf8) else {
            throw LocalizationError.invalidEncoding
        }
        
        var languageTranslations: [String: String] = [:]
        
        let lines = stringsContent.components(separatedBy: .newlines)
        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
            guard !trimmedLine.isEmpty && !trimmedLine.hasPrefix("//") else { continue }
            
            // Parse "key" = "value"; format
            let components = trimmedLine.components(separatedBy: " = ")
            guard components.count == 2 else { continue }
            
            let key = components[0].trimmingCharacters(in: CharacterSet(charactersIn: "\""))
            let value = components[1].trimmingCharacters(in: CharacterSet(charactersIn: "\";"))
            
            languageTranslations[key] = value
        }
        
        translations[language.code] = languageTranslations
    }
    
    private func getDefaultTranslations(for language: SupportedLanguage) -> [String: String] {
        // Return sample translations for demonstration
        switch language {
        case .english:
            return [
                "app.name": "Keeps",
                "tab.people": "People",
                "tab.teams": "Teams",
                "tab.timeline": "Timeline",
                "button.add": "Add",
                "button.save": "Save",
                "button.cancel": "Cancel",
                "placeholder.search": "Search...",
                "title.person_detail": "Person Details",
                "title.team_detail": "Team Details"
            ]
        case .spanish:
            return [
                "app.name": "Keeps",
                "tab.people": "Personas",
                "tab.teams": "Equipos",
                "tab.timeline": "Cronología",
                "button.add": "Agregar",
                "button.save": "Guardar",
                "button.cancel": "Cancelar",
                "placeholder.search": "Buscar...",
                "title.person_detail": "Detalles de Persona",
                "title.team_detail": "Detalles del Equipo"
            ]
        case .french:
            return [
                "app.name": "Keeps",
                "tab.people": "Personnes",
                "tab.teams": "Équipes",
                "tab.timeline": "Chronologie",
                "button.add": "Ajouter",
                "button.save": "Enregistrer",
                "button.cancel": "Annuler",
                "placeholder.search": "Rechercher...",
                "title.person_detail": "Détails de la Personne",
                "title.team_detail": "Détails de l'Équipe"
            ]
        case .german:
            return [
                "app.name": "Keeps",
                "tab.people": "Personen",
                "tab.teams": "Teams",
                "tab.timeline": "Zeitleiste",
                "button.add": "Hinzufügen",
                "button.save": "Speichern",
                "button.cancel": "Abbrechen",
                "placeholder.search": "Suchen...",
                "title.person_detail": "Personendetails",
                "title.team_detail": "Team-Details"
            ]
        case .japanese:
            return [
                "app.name": "Keeps",
                "tab.people": "人々",
                "tab.teams": "チーム",
                "tab.timeline": "タイムライン",
                "button.add": "追加",
                "button.save": "保存",
                "button.cancel": "キャンセル",
                "placeholder.search": "検索...",
                "title.person_detail": "人物詳細",
                "title.team_detail": "チーム詳細"
            ]
        case .chinese:
            return [
                "app.name": "Keeps",
                "tab.people": "人员",
                "tab.teams": "团队",
                "tab.timeline": "时间线",
                "button.add": "添加",
                "button.save": "保存",
                "button.cancel": "取消",
                "placeholder.search": "搜索...",
                "title.person_detail": "人员详情",
                "title.team_detail": "团队详情"
            ]
        }
    }
}

// MARK: - Localization Models

enum SupportedLanguage: String, CaseIterable {
    case english = "English"
    case spanish = "Español"
    case french = "Français"
    case german = "Deutsch"
    case japanese = "日本語"
    case chinese = "中文"

    var code: String {
        switch self {
        case .english: return "en"
        case .spanish: return "es"
        case .french: return "fr"
        case .german: return "de"
        case .japanese: return "ja"
        case .chinese: return "zh"
        }
    }

    var flag: String {
        switch self {
        case .english: return "🇺🇸"
        case .spanish: return "🇪🇸"
        case .french: return "🇫🇷"
        case .german: return "🇩🇪"
        case .japanese: return "🇯🇵"
        case .chinese: return "🇨🇳"
        }
    }

    var isRTL: Bool {
        return false // None of the current languages are RTL
    }
}

enum LocalizationStatus: String, CaseIterable {
    case ready = "Ready"
    case validating = "Validating"
    case incomplete = "Incomplete"
    case complete = "Complete"
    case error = "Error"

    var color: Color {
        switch self {
        case .ready: return .blue
        case .validating: return .orange
        case .incomplete: return .yellow
        case .complete: return .green
        case .error: return .red
        }
    }

    var icon: String {
        switch self {
        case .ready: return "checkmark.circle"
        case .validating: return "clock"
        case .incomplete: return "exclamationmark.triangle"
        case .complete: return "checkmark.circle.fill"
        case .error: return "xmark.circle"
        }
    }
}

enum ExportFormat: String, CaseIterable {
    case json = "JSON"
    case csv = "CSV"
    case xliff = "XLIFF"
    case strings = "Strings"

    var fileExtension: String {
        switch self {
        case .json: return "json"
        case .csv: return "csv"
        case .xliff: return "xliff"
        case .strings: return "strings"
        }
    }

    var mimeType: String {
        switch self {
        case .json: return "application/json"
        case .csv: return "text/csv"
        case .xliff: return "application/x-xliff+xml"
        case .strings: return "text/plain"
        }
    }
}

enum PluralRule {
    case english    // 1: one, other: other
    case slavic     // Complex Slavic rules
    case arabic     // 0: zero, 1: one, 2: two, 3-10: few, 11-99: many, other: other
}

struct MissingTranslation: Identifiable, Equatable {
    let id = UUID()
    let key: String
    let language: SupportedLanguage
    let context: String
    let priority: TranslationPriority
    let timestamp: Date = Date()

    static func == (lhs: MissingTranslation, rhs: MissingTranslation) -> Bool {
        return lhs.key == rhs.key && lhs.language == rhs.language
    }
}

enum TranslationPriority: String, CaseIterable {
    case low = "Low"
    case medium = "Medium"
    case high = "High"
    case critical = "Critical"

    var color: Color {
        switch self {
        case .low: return .green
        case .medium: return .yellow
        case .high: return .orange
        case .critical: return .red
        }
    }

    var icon: String {
        switch self {
        case .low: return "checkmark.circle"
        case .medium: return "exclamationmark.circle"
        case .high: return "exclamationmark.triangle"
        case .critical: return "xmark.octagon"
        }
    }
}

struct LocalizationReport: Identifiable {
    let id = UUID()
    let totalKeys: Int
    let completedLanguages: [SupportedLanguage]
    let missingTranslations: [MissingTranslation]
    let completionPercentage: Double
    let generatedAt: Date

    var isComplete: Bool {
        return missingTranslations.isEmpty
    }

    var criticalMissing: Int {
        return missingTranslations.filter { $0.priority == .critical }.count
    }

    var highMissing: Int {
        return missingTranslations.filter { $0.priority == .high }.count
    }

    var grade: LocalizationGrade {
        switch completionPercentage {
        case 0.95...1.0: return .excellent
        case 0.85..<0.95: return .good
        case 0.70..<0.85: return .fair
        case 0.50..<0.70: return .poor
        default: return .failing
        }
    }
}

enum LocalizationGrade: String, CaseIterable {
    case excellent = "A+"
    case good = "A"
    case fair = "B"
    case poor = "C"
    case failing = "F"

    var color: Color {
        switch self {
        case .excellent: return .green
        case .good: return .blue
        case .fair: return .yellow
        case .poor: return .orange
        case .failing: return .red
        }
    }

    var description: String {
        switch self {
        case .excellent: return "Excellent localization - ready for global release"
        case .good: return "Good localization - minor translations missing"
        case .fair: return "Fair localization - some improvements needed"
        case .poor: return "Poor localization - significant work required"
        case .failing: return "Failing localization - major translations missing"
        }
    }

    var icon: String {
        switch self {
        case .excellent: return "star.fill"
        case .good: return "checkmark.circle.fill"
        case .fair: return "exclamationmark.circle.fill"
        case .poor: return "exclamationmark.triangle.fill"
        case .failing: return "xmark.circle.fill"
        }
    }
}

struct LocalizationStatistics {
    let totalKeys: Int
    let supportedLanguages: Int
    let averageCompletion: Double
    let languageStatistics: [LanguageStatistics]

    var mostCompleteLanguage: LanguageStatistics? {
        return languageStatistics.max { $0.completionPercentage < $1.completionPercentage }
    }

    var leastCompleteLanguage: LanguageStatistics? {
        return languageStatistics.min { $0.completionPercentage < $1.completionPercentage }
    }
}

struct LanguageStatistics: Identifiable {
    let id = UUID()
    let language: SupportedLanguage
    let translatedKeys: Int
    let totalKeys: Int
    let completionPercentage: Double

    var missingKeys: Int {
        return totalKeys - translatedKeys
    }

    var isComplete: Bool {
        return completionPercentage >= 1.0
    }

    var grade: LocalizationGrade {
        switch completionPercentage {
        case 0.95...1.0: return .excellent
        case 0.85..<0.95: return .good
        case 0.70..<0.85: return .fair
        case 0.50..<0.70: return .poor
        default: return .failing
        }
    }
}

enum LocalizationError: Error, LocalizedError {
    case invalidFormat
    case invalidEncoding
    case languageNotFound
    case notImplemented
    case fileNotFound
    case networkError

    var errorDescription: String? {
        switch self {
        case .invalidFormat:
            return "Invalid file format"
        case .invalidEncoding:
            return "Invalid text encoding"
        case .languageNotFound:
            return "Language not found in file"
        case .notImplemented:
            return "Feature not yet implemented"
        case .fileNotFound:
            return "Translation file not found"
        case .networkError:
            return "Network error occurred"
        }
    }
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let languageDidChange = Notification.Name("languageDidChange")
}

// MARK: - String Extensions for Localization

extension String {
    var localized: String {
        return LocalizationManager.shared.localizedString(for: self)
    }

    func localized(arguments: String...) -> String {
        return LocalizationManager.shared.localizedString(for: self, arguments: arguments)
    }

    func localizedPlural(count: Int, arguments: String...) -> String {
        return LocalizationManager.shared.localizedPluralString(for: self, count: count, arguments: arguments)
    }
}
